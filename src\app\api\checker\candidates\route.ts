import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { ilike, or, desc, count, eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const includeResults = searchParams.get('includeResults') === 'true';

    const offset = (page - 1) * limit;

    // Build search conditions
    const searchConditions = search
      ? or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`),
          ilike(candidates.candidateNumber, `%${search}%`)
        )
      : undefined;

    if (includeResults) {
      // Get candidates with their test results
      const candidatesWithResults = await db
        .select({
          id: candidates.id,
          candidateNumber: candidates.candidateNumber,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
          photoUrl: candidates.photoUrl,
          createdAt: candidates.createdAt,
          updatedAt: candidates.updatedAt,
          resultId: testResults.id,
          listeningBandScore: testResults.listeningBandScore,
          readingBandScore: testResults.readingBandScore,
          writingTask1Score: testResults.writingTask1Score,
          writingTask2Score: testResults.writingTask2Score,
          writingBandScore: testResults.writingBandScore,
          speakingBandScore: testResults.speakingBandScore,
          overallBandScore: testResults.overallBandScore,
          status: testResults.status,
        })
        .from(candidates)
        .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
        .where(searchConditions)
        .orderBy(desc(candidates.createdAt))
        .limit(limit)
        .offset(offset);

      // Transform the data to include hasResult flag and nested result object
      const transformedCandidates = candidatesWithResults.map(candidate => ({
        id: candidate.id,
        candidateNumber: candidate.candidateNumber,
        fullName: candidate.fullName,
        email: candidate.email,
        phoneNumber: candidate.phoneNumber,
        dateOfBirth: candidate.dateOfBirth,
        nationality: candidate.nationality,
        passportNumber: candidate.passportNumber,
        testDate: candidate.testDate,
        testCenter: candidate.testCenter,
        photoUrl: candidate.photoUrl,
        createdAt: candidate.createdAt,
        updatedAt: candidate.updatedAt,
        hasResult: !!candidate.resultId,
        result: candidate.resultId ? {
          id: candidate.resultId,
          listeningBandScore: candidate.listeningBandScore,
          readingBandScore: candidate.readingBandScore,
          writingTask1Score: candidate.writingTask1Score,
          writingTask2Score: candidate.writingTask2Score,
          writingBandScore: candidate.writingBandScore,
          speakingBandScore: candidate.speakingBandScore,
          overallBandScore: candidate.overallBandScore,
          status: candidate.status,
        } : null,
      }));

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(candidates)
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      return NextResponse.json({
        candidates: transformedCandidates,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    } else {
      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(candidates)
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      // Get candidates
      const candidatesList = await db
        .select()
        .from(candidates)
        .where(searchConditions)
        .orderBy(desc(candidates.createdAt))
        .limit(limit)
        .offset(offset);

      return NextResponse.json({
        candidates: candidatesList,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    }
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
